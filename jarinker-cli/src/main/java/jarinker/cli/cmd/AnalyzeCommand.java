package jarinker.cli.cmd;

import com.sun.tools.jdeps.JdepsFilter;
import jarinker.core.AnalyzerType;
import jarinker.core.DependencyGraph;
import jarinker.core.JdepsAnalyzer;
import java.nio.file.Path;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.SneakyThrows;
import org.jspecify.annotations.Nullable;
import picocli.CommandLine.Command;
import picocli.CommandLine.Option;
import picocli.CommandLine.Parameters;

/**
 * Analyze command for dependency analysis.
 *
 * <AUTHOR>
 */
@Command(description = "Analyze dependencies and generate dependency graph", mixinStandardHelpOptions = true)
public class AnalyzeCommand implements Runnable {

    @Parameters(description = "Source artifacts to analyze (JAR files or class directories)", arity = "1..*")
    private List<Path> sources;

    @Option(
            names = {"-cp", "-classpath", "--class-path"},
            description = "Classpath entries (can be specified multiple times)",
            required = true)
    private List<Path> classpath;

    // === jdeps options ===

    @Option(
            names = {"--type"},
            description = "Analysis type (class, package, module), see jarinker.core.AnalyzerType",
            defaultValue = "package")
    private AnalyzerType type;

    // Filter options
    @Option(
            names = {"--regex"},
            description = "Find dependencies matching the given pattern")
    private @Nullable Pattern regex;

    // Source filters
    @Option(
            names = {"--include-pattern"},
            description = "Restrict analysis to classes matching pattern")
    private @Nullable Pattern includePattern;

    // === jdeps options end ===

    @Option(
            names = {"--show-jdk-deps"},
            defaultValue = "false",
            description = "Show JDK dependencies, by default they are filtered out")
    private Boolean showJdkDeps;

    @Override
    @SneakyThrows
    public void run() {

        DependencyGraph graph;

        try (var jdepsConfiguration = JdepsAnalyzer.buildJdepsConfiguration(sources, classpath, Runtime.version())) {
            var analyzer = JdepsAnalyzer.builder()
                    .jdepsFilter(buildJdepsFilter())
                    .jdepsConfiguration(jdepsConfiguration)
                    .type(type)
                    .build();

            graph = analyzer.analyze();
        }

        // Print results
        printReport(graph);
    }

    /**
     * Build JdepsFilterBuilder with all configured options.
     *
     * @return configured JdepsFilterBuilder
     */
    private JdepsFilter buildJdepsFilter() {
        var filterBuilder = new JdepsFilter.Builder();

        if (regex != null) {
            filterBuilder.regex(regex);
        }

        filterBuilder.filter(false, false);

        filterBuilder.findJDKInternals(false);

        filterBuilder.findMissingDeps(false);

        if (includePattern != null) {
            filterBuilder.includePattern(includePattern);
        }

        return filterBuilder.build();
    }

    private void printReport(DependencyGraph graph) {
        switch (graph.getAnalysisType()) {
            case CLASS -> printReportForClass(graph);
            case PACKAGE -> printReportForPackage(graph);
            case MODULE -> printReportForModule(graph);
        }
    }

    private void printReportForModule(DependencyGraph graph) {
        printHeader("Module Dependency Analysis");
        printDependenciesByType(graph);
        System.out.println();
        printSummaryStats(graph);
    }

    private void printReportForClass(DependencyGraph graph) {
        printHeader("Class Dependency Analysis");
        printDependenciesByType(graph);
        System.out.println();
        printSummaryStats(graph);
    }

    private void printReportForPackage(DependencyGraph graph) {
        printHeader("Package Dependency Analysis");
        printDependenciesByType(graph);
        System.out.println();
        printSummaryStats(graph);
    }

    private void printHeader(String title) {
        System.out.println("╭─" + "─".repeat(title.length()) + "─╮");
        System.out.println("│ " + title + " │");
        System.out.println("╰─" + "─".repeat(title.length()) + "─╯");
        System.out.println();
    }

    private void printSummaryStats(DependencyGraph graph) {
        var dependenciesMap = graph.getDependenciesMap();
        String nodeTypePlural = getNodeTypePlural();

        // Calculate statistics
        int totalNodes = graph.getNodeCount();
        int usedNodes = 0;
        var showJdkDeps = this.showJdkDeps;

        for (var entry : dependenciesMap.entrySet()) {
            Set<String> deps = entry.getValue().stream()
                    .filter(dep -> showJdkDeps || !isJdkDependency(dep))
                    .filter(dep -> !isSameScopeAsSelf(entry.getKey(), dep))
                    .collect(Collectors.toSet());

            if (!deps.isEmpty()) {
                usedNodes++;
            }
        }

        int unusedNodes = totalNodes - usedNodes;
        double usageRate = totalNodes > 0 ? (double) usedNodes / totalNodes * 100 : 0.0;
        double unusedRate = totalNodes > 0 ? (double) unusedNodes / totalNodes * 100 : 0.0;

        System.out.println("📊 Statistics:");
        System.out.println("   • Total " + nodeTypePlural + ": " + totalNodes);
        System.out.printf("   • Used " + nodeTypePlural + ": %d (%.2f%%)\n", usedNodes, usageRate);
        System.out.printf("   • Unused " + nodeTypePlural + ": %d (%.2f%%)\n", unusedNodes, unusedRate);
    }

    private void printDependenciesByType(DependencyGraph graph) {
        var dependenciesMap = graph.getDependenciesMap();

        if (dependenciesMap.isEmpty()) {
            System.out.println("🔍 No dependencies found.");
            return;
        }

        System.out.println("🔗 Dependencies:");
        System.out.println();

        // Get root nodes from source archives
        Set<String> rootNodes = getRootNodes(graph);

        if (rootNodes.isEmpty()) {
            System.out.println("🔍 No root nodes found in source archives.");
            return;
        }

        // Print dependency tree for each root node
        Set<String> globalVisited = new HashSet<>();
        for (String rootNode : rootNodes.stream().sorted().collect(Collectors.toList())) {
            if (!globalVisited.contains(rootNode)) {
                printTreeNode(rootNode, graph, new HashSet<>(), globalVisited, "", true);
                System.out.println();
            }
        }
    }

    private boolean isSameScopeAsSelf(String source, String dependency) {
        return switch (type) {
            case PACKAGE -> // For package analysis, filter dependencies within the same package
                extractPackageName(source).equals(extractPackageName(dependency));
            case MODULE -> // For module analysis, filter dependencies within the same module
                extractModuleName(source).equals(extractModuleName(dependency));
            case CLASS -> // For class analysis, don't filter same scope dependencies
                false;
        };
    }

    /**
     * Extract package name from a node name.
     * For package analysis, the format is typically "archive/package.name"
     *
     * @param nodeName the node name
     * @return the package name
     */
    private static String extractPackageName(String nodeName) {
        // Handle format like "quick-start-0.1.0.jar/com.example"
        int slashIndex = nodeName.indexOf('/');
        if (slashIndex != -1) {
            return nodeName.substring(slashIndex + 1);
        }
        return nodeName;
    }

    /**
     * Extract module name from a node name.
     * For module analysis, the format is typically "archive/module.name"
     *
     * @param nodeName the node name
     * @return the module name
     */
    private static String extractModuleName(String nodeName) {
        // Handle format like "quick-start-0.1.0.jar/module.name"
        int slashIndex = nodeName.indexOf('/');
        if (slashIndex != -1) {
            return nodeName.substring(slashIndex + 1);
        }
        return nodeName;
    }

    private String getNodeTypePlural() {
        return switch (type) {
            case PACKAGE -> "packages";
            case MODULE -> "modules";
            case CLASS -> "classes";
        };
    }

    /**
     * Check if a dependency is a JDK internal dependency that should be filtered out.
     *
     * @param dependency the dependency name
     * @return true if it's a JDK dependency
     */
    private static boolean isJdkDependency(String dependency) {
        return dependency.startsWith("java.")
                || dependency.startsWith("javax.")
                || dependency.startsWith("jdk.")
                || dependency.startsWith("sun.")
                || dependency.startsWith("com.sun.")
                || dependency.contains("JDK removed internal API");
    }

    /**
     * Get root nodes from source archives.
     *
     * @param graph the dependency graph
     * @return set of root node names
     */
    private Set<String> getRootNodes(DependencyGraph graph) {
        Set<String> rootNodes = new HashSet<>();
        Set<String> rootArchiveNames = graph.getRootArchives().stream()
                .map(archive -> archive.getName())
                .collect(Collectors.toSet());

        for (String node : graph.getDependenciesMap().keySet()) {
            String archiveName = extractArchiveName(node);
            if (rootArchiveNames.contains(archiveName)) {
                rootNodes.add(node);
            }
        }
        return rootNodes;
    }

    /**
     * Extract archive name from a node name.
     * Node names are typically in format "archive-name/package.or.class.name"
     *
     * @param nodeName the node name
     * @return the archive name
     */
    private static String extractArchiveName(String nodeName) {
        int slashIndex = nodeName.indexOf('/');
        return slashIndex != -1 ? nodeName.substring(0, slashIndex) : nodeName;
    }

    /**
     * Get filtered dependencies for a node, applying JDK and same-scope filters.
     *
     * @param node the source node
     * @param graph the dependency graph
     * @return filtered set of dependencies
     */
    private Set<String> getFilteredDependencies(String node, DependencyGraph graph) {
        Set<String> dependencies = graph.getDependenciesMap().getOrDefault(node, Set.of());
        return dependencies.stream()
                .filter(dep -> showJdkDeps || !isJdkDependency(dep))
                .filter(dep -> !isSameScopeAsSelf(node, dep))
                .filter(dep -> !dep.equals(node)) // Filter out self-dependencies
                .collect(Collectors.toSet());
    }

    /**
     * Print a node and its dependencies in tree format.
     *
     * @param node the current node
     * @param graph the dependency graph
     * @param currentPath nodes in the current path (for cycle detection)
     * @param globalVisited all nodes that have been visited globally
     * @param prefix the prefix for tree formatting
     * @param isRoot whether this is a root node
     */
    private void printTreeNode(
            String node,
            DependencyGraph graph,
            Set<String> currentPath,
            Set<String> globalVisited,
            String prefix,
            boolean isRoot) {
        // Check for circular reference
        if (currentPath.contains(node)) {
            System.out.println(prefix + " └── " + node + " (circular reference)");
            return;
        }

        // Add to visited sets
        currentPath.add(node);
        globalVisited.add(node);

        // Print the current node
        if (isRoot) {
            System.out.println("📦 " + node);
        }

        // Get and sort dependencies
        Set<String> dependencies = getFilteredDependencies(node, graph);
        List<String> sortedDeps = dependencies.stream().sorted().collect(Collectors.toList());

        // Print each dependency
        for (int i = 0; i < sortedDeps.size(); i++) {
            String dep = sortedDeps.get(i);
            boolean isLast = (i == sortedDeps.size() - 1);
            String connector = isLast ? " └── " : " ├── ";
            String nextPrefix = prefix + (isLast ? "     " : " │   ");

            // Print the dependency
            if (isRoot) {
                System.out.println(connector + dep);
            } else {
                System.out.println(prefix + connector + dep);
            }

            // Check if this dependency has already been shown
            if (globalVisited.contains(dep)) {
                // Don't expand further, just continue to next dependency
                continue;
            } else {
                // Recursively print sub-dependencies
                if (isRoot) {
                    printTreeNode(
                            dep, graph, new HashSet<>(currentPath), globalVisited, isLast ? "     " : " │   ", false);
                } else {
                    printTreeNode(dep, graph, new HashSet<>(currentPath), globalVisited, nextPrefix, false);
                }
            }
        }

        // Remove from current path (backtrack)
        currentPath.remove(node);
    }
}

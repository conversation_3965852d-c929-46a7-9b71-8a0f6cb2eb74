plugins {
    id "application"
}

def guavaVersion = "33.4.8"
def failureAccessVersion = "1.0.3"
def listenableFutureVersion = "9999.0-empty-to-avoid-conflict-with-guava"
def jspecifyVersion = "1.0.0"
def errorProneVersion = "2.36.0"
def j2objcVersion = "3.0.0"

// Define all dependency jars
def guavaJar = file("libs/guava-${guavaVersion}-jre.jar")
def failureAccessJar = file("libs/failureaccess-${failureAccessVersion}.jar")
def listenableFutureJar = file("libs/listenablefuture-${listenableFutureVersion}.jar")
def jspecifyJar = file("libs/jspecify-${jspecifyVersion}.jar")
def errorProneJar = file("libs/error_prone_annotations-${errorProneVersion}.jar")
def j2objcJar = file("libs/j2objc-annotations-${j2objcVersion}.jar")

tasks.register("downloadGuava", Exec) {
    commandLine "curl", "--create-dirs", "-o", guavaJar.absolutePath,
            "https://repo1.maven.org/maven2/com/google/guava/guava/${guavaVersion}-jre/guava-${guavaVersion}-jre.jar"
    outputs.file guavaJar
}

tasks.register("downloadFailureAccess", Exec) {
    commandLine "curl", "--create-dirs", "-o", failureAccessJar.absolutePath,
            "https://repo1.maven.org/maven2/com/google/guava/failureaccess/${failureAccessVersion}/failureaccess-${failureAccessVersion}.jar"
    outputs.file failureAccessJar
}

tasks.register("downloadListenableFuture", Exec) {
    commandLine "curl", "--create-dirs", "-o", listenableFutureJar.absolutePath,
            "https://repo1.maven.org/maven2/com/google/guava/listenablefuture/${listenableFutureVersion}/listenablefuture-${listenableFutureVersion}.jar"
    outputs.file listenableFutureJar
}

tasks.register("downloadJspecify", Exec) {
    commandLine "curl", "--create-dirs", "-o", jspecifyJar.absolutePath,
            "https://repo1.maven.org/maven2/org/jspecify/jspecify/${jspecifyVersion}/jspecify-${jspecifyVersion}.jar"
    outputs.file jspecifyJar
}

tasks.register("downloadErrorProne", Exec) {
    commandLine "curl", "--create-dirs", "-o", errorProneJar.absolutePath,
            "https://repo1.maven.org/maven2/com/google/errorprone/error_prone_annotations/${errorProneVersion}/error_prone_annotations-${errorProneVersion}.jar"
    outputs.file errorProneJar
}

tasks.register("downloadJ2objc", Exec) {
    commandLine "curl", "--create-dirs", "-o", j2objcJar.absolutePath,
            "https://repo1.maven.org/maven2/com/google/j2objc/j2objc-annotations/${j2objcVersion}/j2objc-annotations-${j2objcVersion}.jar"
    outputs.file j2objcJar
}

dependencies {
    if (file("shrunk-libs/guava-${guavaVersion}-jre.jar").exists()) {
        implementation files("shrunk-libs/guava-${guavaVersion}-jre.jar")
    } else {
        implementation files(guavaJar).builtBy(tasks.named("downloadGuava"))
    }

    if (file("shrunk-libs/failureaccess-${failureAccessVersion}.jar").exists()) {
        implementation files("shrunk-libs/failureaccess-${failureAccessVersion}.jar")
    } else {
        implementation files(failureAccessJar).builtBy(tasks.named("downloadFailureAccess"))
    }

    if (file("shrunk-libs/listenablefuture-${listenableFutureVersion}.jar").exists()) {
        implementation files("shrunk-libs/listenablefuture-${listenableFutureVersion}.jar")
    } else {
        implementation files(listenableFutureJar).builtBy(tasks.named("downloadListenableFuture"))
    }

    if (file("shrunk-libs/jspecify-${jspecifyVersion}.jar").exists()) {
        implementation files("shrunk-libs/jspecify-${jspecifyVersion}.jar")
    } else {
        implementation files(jspecifyJar).builtBy(tasks.named("downloadJspecify"))
    }

    if (file("shrunk-libs/error_prone_annotations-${errorProneVersion}.jar").exists()) {
        implementation files("shrunk-libs/error_prone_annotations-${errorProneVersion}.jar")
    } else {
        implementation files(errorProneJar).builtBy(tasks.named("downloadErrorProne"))
    }

    if (file("shrunk-libs/j2objc-annotations-${j2objcVersion}.jar").exists()) {
        implementation files("shrunk-libs/j2objc-annotations-${j2objcVersion}.jar")
    } else {
        implementation files(j2objcJar).builtBy(tasks.named("downloadJ2objc"))
    }
}

application {
    mainClass = "com.example.QuickStart"
}
